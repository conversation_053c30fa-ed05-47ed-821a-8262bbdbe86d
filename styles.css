:root {
    /* Dark theme (default) */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: rgba(255, 255, 255, 0.05);
    --bg-card-hover: rgba(255, 255, 255, 0.08);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --text-muted: rgba(255, 255, 255, 0.6);
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --accent-tertiary: #ec4899;
    --accent-quaternary: #06b6d4;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --border-color: rgba(255, 255, 255, 0.1);
    --border-color-hover: rgba(255, 255, 255, 0.2);
    --shadow-color: rgba(0, 0, 0, 0.3);
    --shadow-color-light: rgba(0, 0, 0, 0.1);
    --navbar-bg: rgba(10, 10, 10, 0.9);
    --navbar-bg-scroll: rgba(10, 10, 10, 0.95);
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);

    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    --transition-extra-slow: 0.8s;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;
}

[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-card: rgba(0, 0, 0, 0.05);
    --bg-card-hover: rgba(0, 0, 0, 0.08);
    --text-primary: #1a202c;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --accent-primary: #6366f1;
    --accent-secondary: #8b5cf6;
    --accent-tertiary: #ec4899;
    --accent-quaternary: #06b6d4;
    --accent-success: #10b981;
    --accent-warning: #f59e0b;
    --border-color: rgba(0, 0, 0, 0.1);
    --border-color-hover: rgba(0, 0, 0, 0.2);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --shadow-color-light: rgba(0, 0, 0, 0.05);
    --navbar-bg: rgba(255, 255, 255, 0.9);
    --navbar-bg-scroll: rgba(255, 255, 255, 0.95);
    --glass-bg: rgba(0, 0, 0, 0.05);
    --glass-border: rgba(0, 0, 0, 0.1);

    /* Animation Timings */
    --transition-fast: 0.2s;
    --transition-normal: 0.3s;
    --transition-slow: 0.5s;
    --transition-extra-slow: 0.8s;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 12px;
    --radius-lg: 16px;
    --radius-xl: 20px;
    --radius-full: 50px;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    scroll-padding-top: 80px;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--accent-secondary), var(--accent-tertiary));
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    overflow-x: hidden;
    transition: background 0.3s ease, color 0.3s ease;
    cursor: none;
}

/* Custom Cursor */
.custom-cursor {
    position: fixed;
    width: 20px;
    height: 20px;
    background: var(--accent-primary);
    border-radius: 50%;
    pointer-events: none;
    z-index: 9999;
    transition: transform 0.1s ease;
    mix-blend-mode: difference;
}

.custom-cursor.hover {
    transform: scale(2);
    background: var(--accent-secondary);
}

@media (max-width: 768px) {
    body {
        cursor: auto;
    }

    .custom-cursor {
        display: none;
    }
}

body.loading {
    overflow: hidden;
}

/* Page Loader */
.page-loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.page-loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.loader-content {
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.loader-logo {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.logo-text {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    animation: logoGlow 2s ease-in-out infinite alternate;
}

@keyframes logoGlow {
    from {
        text-shadow: 0 0 10px var(--accent-primary);
    }
    to {
        text-shadow: 0 0 20px var(--accent-primary), 0 0 30px var(--accent-secondary);
    }
}

.loader-progress {
    width: 200px;
    height: 4px;
    background: var(--border-color);
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 2px;
    width: 0%;
    transition: width 0.3s ease;
}

.loader-animation {
    display: flex;
    gap: 0.5rem;
}

.loader-circle {
    width: 12px;
    height: 12px;
    background: var(--accent-primary);
    border-radius: 50%;
    animation: loaderBounce 1.4s ease-in-out infinite both;
}

.loader-circle:nth-child(1) { animation-delay: -0.32s; }
.loader-circle:nth-child(2) { animation-delay: -0.16s; }
.loader-circle:nth-child(3) { animation-delay: 0s; }

@keyframes loaderBounce {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Enhanced Animations and Micro-interactions */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-on-scroll.animated {
    opacity: 1;
    transform: translateY(0);
}

.stagger-animation {
    animation-delay: var(--stagger-delay, 0s);
}

/* Smooth transitions for all interactive elements */
a, button, input, textarea, select {
    transition: all 0.3s ease;
}

/* Enhanced hover effects */
.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px var(--shadow-color);
}

.hover-glow {
    position: relative;
    overflow: hidden;
}

.hover-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.hover-glow:hover::before {
    left: 100%;
}

/* Pulse animation for important elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

/* Shake animation for errors */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Slide in animations */
.slide-in-left {
    animation: slideInLeft 0.6s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.6s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--navbar-bg);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    transition: background 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    transition: color 0.3s ease;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-primary);
    transition: width 0.3s ease;
}

.nav-menu a:hover {
    color: var(--accent-primary);
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Navigation Controls */
.nav-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 50px;
    width: 50px;
    height: 30px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 5px;
    overflow: hidden;
}

.theme-toggle::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: var(--accent-primary);
    border-radius: 50%;
    transition: transform 0.3s ease;
    z-index: 1;
}

[data-theme="light"] .theme-toggle::before {
    transform: translateX(20px);
}

.sun-icon, .moon-icon {
    font-size: 14px;
    z-index: 2;
    transition: opacity 0.3s ease;
}

.sun-icon {
    opacity: 0;
}

.moon-icon {
    opacity: 1;
}

[data-theme="light"] .sun-icon {
    opacity: 1;
}

[data-theme="light"] .moon-icon {
    opacity: 0;
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    gap: 4px;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: all 0.3s ease;
    border-radius: 2px;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    padding: 0 2rem;
    background:
        radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
    overflow: hidden;
}

/* Particles Background */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

#particles-canvas {
    width: 100%;
    height: 100%;
}

.hero-content {
    max-width: 1200px;
    margin: 0 auto;
    color: var(--text-primary);
    position: relative;
    z-index: 2;
    animation: fadeInUp 1s ease-out;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 4rem;
    align-items: center;
    width: 100%;
}

.hero-text {
    max-width: 600px;
}

.hero-stats {
    padding: 2rem;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    min-width: 200px;
    animation: fadeInRight 0.8s ease-out 0.6s both;
}

.hero-stats .stat-item {
    text-align: center;
    padding: 1rem;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.hero-stats .stat-item:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.1);
}

.hero-stats .stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.hero-stats .stat-label {
    font-size: 0.9rem;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-intro {
    margin-bottom: 1rem;
}

.greeting {
    font-size: 1.2rem;
    color: var(--accent-secondary);
    font-weight: 500;
    display: block;
    margin-bottom: 0.5rem;
    animation: fadeInLeft 0.8s ease-out 0.2s both;
}

.hero-name {
    font-size: clamp(2.5rem, 6vw, 4.5rem);
    font-weight: 800;
    margin: 0;
    background: linear-gradient(135deg, var(--text-primary) 0%, var(--accent-primary) 50%, var(--accent-secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: fadeInLeft 0.8s ease-out 0.4s both, gradientShift 4s ease-in-out infinite;
    position: relative;
    line-height: 1.1;
    letter-spacing: -0.02em;
}

.hero-name::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 2px;
    animation: fadeInLeft 0.8s ease-out 0.6s both;
}

.hero-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    min-height: 4rem;
}

.typing-text {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
}

.cursor {
    color: var(--accent-primary);
    animation: blink 1s infinite;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2.5rem;
    opacity: 0.8;
    animation: fadeInUp 0.8s ease-out 0.8s both;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease-out 1s both;
}

.btn-primary, .btn-secondary {
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    color: #fff;
    border: none;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
    background: transparent;
    color: #fff;
    border: 2px solid #6366f1;
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: #6366f1;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(99, 102, 241, 0.2);
}

.arrow {
    transition: transform 0.3s ease;
    font-style: normal;
}

.btn-primary:hover .arrow,
.btn-secondary:hover .arrow {
    transform: translateX(5px);
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
    animation: fadeInUp 0.8s ease-out 1.2s both;
}

.scroll-arrow {
    width: 2px;
    height: 30px;
    background: linear-gradient(to bottom, transparent, #6366f1);
    position: relative;
    animation: scrollBounce 2s infinite;
}

.scroll-arrow::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 6px solid #6366f1;
}

@keyframes scrollBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(10px); }
}

/* Hero Visual Elements */
.hero-visual {
    position: absolute;
    right: 0;
    top: 0;
    width: 50%;
    height: 100%;
    z-index: 1;
    pointer-events: none;
}

.floating-elements {
    position: relative;
    width: 100%;
    height: 100%;
}

.floating-card {
    position: absolute;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-1 {
    right: 15%;
    top: 20%;
    width: 200px;
    height: 250px;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(139, 92, 246, 0.1));
    animation: float1 8s ease-in-out infinite;
}

.card-2 {
    right: 5%;
    top: 60%;
    width: 150px;
    height: 180px;
    background: linear-gradient(135deg, rgba(236, 72, 153, 0.1), rgba(245, 158, 11, 0.1));
    animation: float2 6s ease-in-out infinite 1s;
}

.card-3 {
    right: 25%;
    top: 45%;
    width: 100px;
    height: 120px;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(59, 130, 246, 0.1));
    animation: float3 7s ease-in-out infinite 2s;
}

.hero-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    right: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05), rgba(139, 92, 246, 0.05));
}

.shape-1 {
    width: 300px;
    height: 300px;
    right: -50px;
    top: 10%;
    animation: rotate 20s linear infinite;
}

.shape-2 {
    width: 150px;
    height: 150px;
    right: 20%;
    bottom: 20%;
    animation: rotate 15s linear infinite reverse;
}

.shape-3 {
    width: 80px;
    height: 80px;
    right: 40%;
    top: 30%;
    animation: pulse 4s ease-in-out infinite;
}

/* Animations */
@keyframes float1 {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(2deg); }
    66% { transform: translateY(10px) rotate(-1deg); }
}

@keyframes float2 {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(3deg); }
}

@keyframes float3 {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    25% { transform: translateY(-10px) rotate(-2deg); }
    75% { transform: translateY(5px) rotate(1deg); }
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.5; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Sections */
.about, .projects, .contact {
    padding: 5rem 0;
    color: var(--text-primary);
}

.section-title {
    font-size: 3rem;
    text-align: center;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* About Section */
.about {
    background:
        radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 20% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%),
        var(--bg-primary);
    position: relative;
}

.about::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
}

.about-content {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.about-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: start;
}

.about-text {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.about-intro h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.about-intro p,
.about-details p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

/* About Sections */
.about-objective,
.about-experience,
.about-achievements,
.about-certifications,
.about-education {
    margin-bottom: 2.5rem;
}

.about-objective h4,
.about-experience h4,
.about-achievements h4,
.about-certifications h4,
.about-education h4 {
    font-size: 1.4rem;
    color: var(--accent-primary);
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1rem;
}

.about-objective h4::before,
.about-experience h4::before,
.about-achievements h4::before,
.about-certifications h4::before,
.about-education h4::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 2px;
}

.about-objective p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    font-style: italic;
    padding: 1rem;
    background: var(--bg-card);
    border-radius: 10px;
    border-left: 4px solid var(--accent-primary);
}

/* Experience Timeline */
.experience-timeline,
.education-timeline {
    position: relative;
    padding-left: 2rem;
}

.experience-timeline::before,
.education-timeline::before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, var(--accent-primary), var(--accent-secondary));
}

.experience-item,
.education-item {
    position: relative;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.experience-item::before,
.education-item::before {
    content: '';
    position: absolute;
    left: -2.5rem;
    top: 1.5rem;
    width: 12px;
    height: 12px;
    background: var(--accent-primary);
    border-radius: 50%;
    border: 3px solid var(--bg-primary);
}

.experience-item:hover,
.education-item:hover {
    transform: translateX(5px);
    border-color: var(--accent-primary);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.experience-header,
.education-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.experience-header h5,
.education-header h5 {
    color: var(--text-primary);
    font-size: 1.1rem;
    margin: 0;
    flex: 1;
}

.experience-date,
.education-date {
    color: var(--accent-primary);
    font-size: 0.9rem;
    font-weight: 500;
    background: rgba(99, 102, 241, 0.1);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
}

.experience-location,
.education-institution {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.experience-desc,
.education-grade {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Achievements Grid */
.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.achievement-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.achievement-item:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 30px var(--shadow-color);
}

.achievement-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 15px;
    flex-shrink: 0;
}

.achievement-content h5 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.achievement-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

/* Certifications List */
.certifications-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-card);
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.cert-item:hover {
    transform: translateY(-3px);
    border-color: var(--accent-primary);
    box-shadow: 0 5px 15px var(--shadow-color);
}

.cert-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(99, 102, 241, 0.1);
    border-radius: 8px;
    flex-shrink: 0;
}

.cert-details h5 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.cert-details p {
    color: var(--text-muted);
    font-size: 0.9rem;
    margin: 0;
}

.about-highlights {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.5rem;
    margin-top: 2rem;
}

.highlight-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.highlight-item:hover {
    transform: translateY(-5px);
}

.highlight-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 15px;
    flex-shrink: 0;
}

.highlight-content h5 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.highlight-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
    font-size: 0.9rem;
}

/* Skills Section */
.skills-section h3 {
    font-size: 1.8rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
}

.skills-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.skill-category h4 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: var(--accent-primary);
}

.skill-bars {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.skill-bar {
    background: var(--bg-card);
    padding: 1rem;
    border-radius: 10px;
    border: 1px solid var(--border-color);
}

.skill-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.skill-progress {
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    overflow: hidden;
}

.skill-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 3px;
    width: 0%;
    transition: width 1.5s ease-in-out;
}

/* Tools Section */
.tools-section {
    margin-top: 2rem;
}

.tools-section h4 {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
    color: var(--accent-primary);
}

.tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.tool-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: var(--bg-card);
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
}

.tool-item:hover {
    transform: translateY(-5px);
    border-color: var(--accent-primary);
    box-shadow: 0 10px 30px var(--shadow-color);
}

.tool-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.tool-item span:last-child {
    font-size: 0.9rem;
    color: var(--text-secondary);
    text-align: center;
}

/* Projects */
.project-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 25px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--accent-primary);
    color: white;
    border-color: var(--accent-primary);
    transform: translateY(-2px);
}

.projects {
    background:
        radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.05) 0%, transparent 50%),
        var(--bg-primary);
    position: relative;
}

.projects::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ec4899' fill-opacity='0.02' fill-rule='evenodd'%3E%3Cpath d='m0 40l40-40h-40v40zm40 0v-40h-40l40 40z'/%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
}

.projects .container {
    position: relative;
    z-index: 1;
}

/* Featured Project Showcase */
.featured-project {
    margin-bottom: 4rem;
    padding: 3rem;
    position: relative;
    overflow: hidden;
}

.featured-project::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    opacity: 0.5;
}

.featured-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.featured-badge {
    display: inline-block;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1rem;
}

.featured-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.2;
}

.featured-description {
    font-size: 1.1rem;
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.featured-tech {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.tech-tag {
    background: rgba(99, 102, 241, 0.1);
    color: var(--accent-primary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.featured-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.featured-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.featured-image {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.featured-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
}

.image-placeholder {
    font-size: 4rem;
    opacity: 0.8;
}

.projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
    gap: 2rem;
    align-items: start;
}

.project-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--glass-border);
    cursor: pointer;
    opacity: 1;
    transform: scale(1);
    position: relative;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.project-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow:
        0 25px 50px var(--shadow-color),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    border-color: var(--accent-primary);
}

.project-card:hover::before {
    opacity: 1;
}

.project-card.hidden {
    opacity: 0;
    transform: scale(0.8);
    pointer-events: none;
}

.project-image {
    height: 200px;
    /* background-image: url("${project.images[0]}"); */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    overflow: hidden;
}

.project-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-image::before {
    opacity: 1;
}

.project-image::after {
    content: 'View Details';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-weight: 600;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover .project-image::after {
    opacity: 1;
}

.project-content {
    padding: 1.5rem;
}

.project-content h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-size: 1.25rem;
}

.project-content p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.project-tags {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.project-tag {
    background: rgba(99, 102, 241, 0.1);
    color: var(--accent-primary);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-link {
    color: var(--accent-primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.project-link:hover {
    color: var(--accent-secondary);
}

/* Project Modal */
.project-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    display: none;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.project-modal.active {
    display: flex;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    background: var(--bg-primary);
    border-radius: 20px;
    max-width: 1000px;
    max-height: 90vh;
    width: 100%;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 1.5rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
    z-index: 10;
}

.modal-close:hover {
    background: var(--accent-primary);
    color: white;
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.modal-header h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.modal-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.modal-body {
    padding: 2rem;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
}

.modal-image-gallery {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.main-image {
    border-radius: 10px;
    overflow: hidden;
    aspect-ratio: 16/10;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-thumbnails {
    display: flex;
    gap: 0.5rem;
    overflow-x: auto;
}

.thumbnail {
    width: 60px;
    height: 40px;
    border-radius: 5px;
    overflow: hidden;
    cursor: pointer;
    opacity: 0.6;
    transition: opacity 0.3s ease;
    flex-shrink: 0;
}

.thumbnail:hover,
.thumbnail.active {
    opacity: 1;
}

.thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.modal-details h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

.project-description p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-features {
    margin-bottom: 2rem;
}

.project-features ul {
    list-style: none;
    padding: 0;
}

.project-features li {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.project-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-primary);
    font-weight: bold;
}

.tech-stack {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 2rem;
}

.tech-item {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.project-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Contact */
.contact {
    background:
        radial-gradient(circle at 50% 50%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    position: relative;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.02'%3E%3Cpath d='M0 0h80v80H0V0zm20 20v40h40V20H20zm20 35a15 15 0 1 1 0-30 15 15 0 0 1 0 30z' fill-rule='nonzero'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.3;
}

.contact-content {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    z-index: 1;
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-intro h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.contact-intro p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-method {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    border-radius: 15px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
}

.contact-method:hover {
    transform: translateY(-5px);
}

.method-icon {
    font-size: 1.5rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));
    border-radius: 10px;
}

.method-details h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.method-details p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.social-links h4 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.social-icons {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1.2rem;
}

.social-link:hover {
    background: var(--accent-primary);
    border-color: var(--accent-primary);
    transform: translateY(-3px);
}

/* Contact Form */
.contact-form-container {
    position: relative;
}

.contact-form {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    padding: 2.5rem;
    border-radius: 20px;
    border: 1px solid var(--glass-border);
    position: relative;
}

.contact-form::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 20px;
    pointer-events: none;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    font-weight: 500;
    font-size: 0.9rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-primary);
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-primary);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.form-error {
    color: #ef4444;
    font-size: 0.8rem;
    margin-top: 0.25rem;
    display: block;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.form-error.show {
    opacity: 1;
}

.submit-btn {
    width: 100%;
    position: relative;
    overflow: hidden;
}

.btn-loading {
    display: none;
}

.submit-btn.loading .btn-text {
    display: none;
}

.submit-btn.loading .btn-loading {
    display: inline;
}

.submit-btn.loading .arrow {
    display: none;
}

.form-success {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-card);
    border-radius: 15px;
    border: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 2rem;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.form-success.show {
    opacity: 1;
    visibility: visible;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.form-success h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
}

.form-success p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Modern Utility Classes */
.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    position: relative;
    overflow: hidden;
}

.glass-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.glass-card:hover::before {
    left: 100%;
}

.gradient-text {
    background: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradientShift 3s ease-in-out infinite;
}

.floating-animation {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Parallax Effect */
.parallax-bg {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
}

/* Magnetic Effect */
.magnetic {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic:hover {
    transform: scale(1.05) rotate(2deg);
}

.scale-on-hover {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-on-hover:hover {
    transform: scale(1.05);
}

.glow-on-hover {
    transition: all 0.3s ease;
}

.glow-on-hover:hover {
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.3);
}

/* Enhanced Section Spacing */
.section-padding {
    padding: 6rem 0;
}

.section-padding-large {
    padding: 8rem 0;
}

/* Modern Grid Layouts */
.grid-auto-fit {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.grid-auto-fill {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
}

/* Responsive */
@media (max-width: 1024px) {
    .about-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .about-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        right: -100%;
        width: 300px;
        height: calc(100vh - 80px);
        background: var(--bg-primary);
        border-left: 1px solid var(--border-color);
        flex-direction: column;
        padding: 2rem;
        gap: 1.5rem;
        transition: right 0.3s ease;
        z-index: 999;
        box-shadow: -5px 0 15px var(--shadow-color);
    }

    .nav-menu.active {
        right: 0;
        display: flex;
    }

    .mobile-menu-toggle {
        display: flex;
        z-index: 1000;
    }

    .mobile-menu-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-menu-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .mobile-menu-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .hero-stats {
        flex-direction: row;
        justify-content: center;
        min-width: auto;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        width: 100%;
    }

    .hero-buttons .btn-primary,
    .hero-buttons .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .hero-visual {
        display: none;
    }

    .featured-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .featured-title {
        font-size: 2rem;
    }

    .featured-actions {
        justify-content: center;
    }

    .about-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .tools-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .certifications-list {
        grid-template-columns: 1fr;
    }

    .experience-timeline,
    .education-timeline {
        padding-left: 1rem;
    }

    .experience-timeline::before,
    .education-timeline::before {
        left: 0.5rem;
    }

    .experience-item::before,
    .education-item::before {
        left: -1.5rem;
    }

    .experience-header,
    .education-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .projects-grid {
        grid-template-columns: 1fr;
    }

    .project-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    .modal-body {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .modal-header {
        padding: 1.5rem 1.5rem 1rem;
    }

    .modal-header h2 {
        font-size: 1.5rem;
    }

    .project-actions {
        flex-direction: column;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-methods {
        gap: 1rem;
    }

    .social-icons {
        justify-content: center;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .hero-content {
        padding: 0 1rem;
        gap: 1.5rem;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-stats .stat-item {
        padding: 0.75rem;
    }

    .hero-stats .stat-number {
        font-size: 1.5rem;
    }

    .featured-project {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .featured-title {
        font-size: 1.5rem;
    }

    .featured-actions {
        flex-direction: column;
    }

    .featured-actions .btn-primary,
    .featured-actions .btn-secondary {
        width: 100%;
        justify-content: center;
    }

    .tools-grid {
        grid-template-columns: 1fr;
    }

    .about-highlights {
        gap: 1rem;
    }

    .highlight-item {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .contact-methods {
        gap: 0.75rem;
    }

    .contact-method {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
    }

    .section-title {
        font-size: 2rem;
    }
}