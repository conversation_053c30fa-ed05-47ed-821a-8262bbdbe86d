<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title><PERSON><PERSON></title>
    <link rel="icon" type="image/x-icon" href="./favicon.ico">    

    <meta name="description"
        content="Detail-oriented IT engineering student with hands-on experience in web development, AI/ML, robotics, Automation and embedded systems Passionate about delivering impactful real-world solutions." />
    <meta name="keywords"
        content="<PERSON>hil Katariya, Full Stack Developer, AI Engineer, Web Development, Machine Learning, Robotics, Python, JavaScript, React" />
    <meta name="author" content="Sahil Katariya" />

    <!-- Open Graph Tags -->
    <meta property="og:title" content="Sahil Katariya - Full Stack Developer & AI Engineer" />
    <meta property="og:description"
        content="Detail-oriented IT engineering student with hands-on experience in web development, AI/ML, robotics, Automation and embedded systems" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://sahilkatariya.dev" />
    <!-- <meta property="og:image" content="https://sahilkatariya.dev/og-image.jpg" /> -->

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Sahil Katariya - Full Stack Developer & AI Engineer" />
    <meta name="twitter:description"
        content="Detail-oriented IT engineering student with hands-on experience in web development, AI/ML, robotics, Automation and embedded systems" />
    <!-- <meta name="twitter:image" content="https://sahilkatariya.dev/og-image.jpg" /> -->

    <!-- Additional SEO Tags -->
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    <link rel="canonical" href="https://sahilkatariya.dev" />

    <!-- Performance Optimizations -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />

    <!-- Structured Data -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "Person",
        "name": "Sahil Katariya",
        "jobTitle": "Full Stack Developer & AI Engineer",
        "description": "Detail-oriented IT engineering student with hands-on experience in web development, AI/ML, robotics, Automation and embedded systems",
        "url": "https://sahilkatariya.dev",
        "email": "<EMAIL>",
        "telephone": "+91 8980657423",
        "address": {
          "@type": "PostalAddress",
          "addressLocality": "Bhavnagar",
          "addressRegion": "Gujarat",
          "addressCountry": "India"
        },
        "alumniOf": {
          "@type": "EducationalOrganization",
          "name": "Government Engineering College, Bhavnagar"
        },
        "knowsAbout": [
          "Web Development",
          "Frontend Development",
          "AI/ML",
          "Backend Development",
          "Robotics",
          "Computer Vision",
          "Python",
          "JavaScript",
          "React"
        ]
      }
    </script>
    <link rel="stylesheet" href="styles.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
        rel="stylesheet" />
</head>

<body>
    <!-- Page Loader -->
    <div class="page-loader" id="page-loader">
        <div class="loader-content">
            <div class="loader-logo">
                <div class="logo-text">Sahil Katariya</div>
                <div class="loader-progress">
                    <div class="progress-bar" id="progress-bar"></div>
                </div>
            </div>
            <div class="loader-animation">
                <div class="loader-circle"></div>
                <div class="loader-circle"></div>
                <div class="loader-circle"></div>
            </div>
        </div>
    </div>

    <nav class="navbar">
        <div class="nav-container">
            <div class="logo">Sahil Katariya</div>
            <ul class="nav-menu">
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#projects">Projects</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <span class="sun-icon">☀️</span>
                    <span class="moon-icon">🌙</span>
                </button>
                <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle menu">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <section id="home" class="hero">
        <div class="particles-container">
            <canvas id="particles-canvas"></canvas>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <div class="hero-intro">
                    <span class="greeting">Hello, I'm</span>
                    <h1 class="hero-name" id="hero-name">Sahil Katariya</h1>
                </div>
                <h2 class="hero-title">
                    <span class="typing-text" id="typing-text"></span>
                    <span class="cursor">|</span>
                </h2>
                <p class="hero-subtitle">
                    Detail-oriented IT engineering student passionate about delivering
                    impactful real-world solutions through full-stack and AI technologies
                </p>
                <div class="hero-buttons">
                    <a href="#about" class="btn-primary">
                        <span>About Me</span>
                        <i class="arrow">→</i>
                    </a>
                    <a href="#contact" class="btn-secondary">
                        <span>Get In Touch</span>
                        <i class="arrow">→</i>
                    </a>
                </div>
            </div>
            <div class="scroll-indicator">
                <div class="scroll-arrow"></div>
                <span>Scroll to explore</span>
            </div>
        </div>
        <div class="hero-visual">
            <div class="floating-elements">
                <div class="floating-card card-1"></div>
                <div class="floating-card card-2"></div>
                <div class="floating-card card-3"></div>
            </div>
            <div class="hero-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
            </div>
        </div>
    </section>

    <section id="about" class="about">
        <div class="container">
            <h2 class="section-title">About Me</h2>
            <div class="about-content">
                <div class="about-grid">
                    
                    <div class="about-text">
                        <div class="about-intro">
                            <h3>Hello! I'm Sahil Katariya</h3>
                            <p>
                                A detail-oriented IT engineering student from Bhavnagar, Gujarat, India, with hands-on experience in web development, AI/ML, robotics and embedded systems. Currently pursuing Bachelor of Engineering in Information Technology at Government Engineering College, Bhavnagar with an impressive CGPA of 7.17.
                            </p>
                        </div>

                        <div class="about-objective">
                            <h4>Professional Objective</h4>
                            <p>
                                Passionate about delivering impactful real-world solutions and growing in a collaborative, innovation-driven environment through full-stack and AI technologies. Seeking opportunities to apply my technical expertise in creating cutting-edge solutions that make a difference.
                            </p>
                        </div>
                        
                
                        <div class="about-experience">
                            <h4>Professional Experience</h4>
                            <div class="experience-timeline">
                                <div class="experience-item">
                                    <div class="experience-header">
                                        <h5>Executive Member - BitsToBytes</h5>
                                        <span class="experience-date">Aug 2023 - Present</span>
                                    </div>
                                    <p class="experience-location">Bhavnagar</p>
                                    <p class="experience-desc">Shared technical ideas, supported team coding sessions, and encouraged collaborative learning in a dynamic tech community.</p>
                                </div>

                                <div class="experience-item">
                                    <div class="experience-header">
                                        <h5>Executive, Finance Club</h5>
                                        <span class="experience-date">Jan 2022 - Present</span>
                                    </div>
                                    <p class="experience-location">Bhavnagar</p>
                                    <p class="experience-desc">Coordinated events, managed finances, sponsorships, and volunteer operations while developing leadership and organizational skills.</p>
                                </div>

                                <div class="experience-item">
                                    <div class="experience-header">
                                        <h5>UI/UX Designer - GDG College Chapter</h5>
                                        <span class="experience-date">Jan 2024 - Present</span>
                                    </div>
                                    <p class="experience-location">Bhavnagar</p>
                                    <p class="experience-desc">Designed intuitive UI and apps, ensured brand alignment, and assisted with frontend development for various college tech initiatives.</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="about-highlights">
                            <div class="highlight-item glass-card scale-on-hover">
                                <div class="highlight-icon">🚀</div>
                                <div class="highlight-content">
                                    <h5>Innovation Focus</h5>
                                    <p>Passionate about cutting-edge technologies and innovative solutions</p>
                                </div>
                            </div>
                            <div class="highlight-item glass-card scale-on-hover">
                                <div class="highlight-icon">🎯</div>
                                <div class="highlight-content">
                                    <h5>Problem Solver</h5>
                                    <p>Analytical mindset with focus on delivering impactful real-world solutions</p>
                                </div>
                            </div>
                            <div class="highlight-item glass-card scale-on-hover">
                                <div class="highlight-icon">🌟</div>
                                <div class="highlight-content">
                                    <h5>Team Player</h5>
                                    <p>Collaborative approach with strong leadership and communication skills</p>
                                </div>
                            </div>
                        </div>

                        <div class="about-achievements">
                            <h4>Key Achievements</h4>
                            <div class="achievements-grid">
                                <div class="achievement-item">
                                    <div class="achievement-icon">🏆</div>
                                    <div class="achievement-content">
                                        <h5>Team Lead - DhruvSankalp</h5>
                                        <p>Led autonomous aerial navigation project shortlisted by ISRO-URSC, demonstrating exceptional leadership in cutting-edge robotics.</p>
                                    </div>
                                </div>
                                <div class="achievement-item">
                                    <div class="achievement-icon">👥</div>
                                    <div class="achievement-content">
                                        <h5>Google Developer Group (GDG)</h5>
                                        <p>Core team member and UI/UX designer contributing to innovative tech solutions and community building.</p>
                                    </div>
                                </div>
                                <div class="achievement-item">
                                    <div class="achievement-icon">🏗️</div>
                                    <div class="achievement-content">
                                        <h5>System Architect</h5>
                                        <p>Designed custom drone modular firmware architecture showcasing advanced system design capabilities.</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="about-certifications">
                            <h4>Certifications & Learning</h4>
                            <div class="certifications-list">
                                <div class="cert-item">
                                    <span class="cert-icon">🤖</span>
                                    <div class="cert-details">
                                        <h5>AI for Manufacturing</h5>
                                        <p>Intel & GTC</p>
                                    </div>
                                </div>
                                <div class="cert-item">
                                    <span class="cert-icon">🐍</span>
                                    <div class="cert-details">
                                        <h5>Python for Data Science</h5>
                                        <p>NPTEL</p>
                                    </div>
                                </div>
                                <div class="cert-item">
                                    <span class="cert-icon">⚛️</span>
                                    <div class="cert-details">
                                        <h5>React Frontend Development</h5>
                                        <p>Udemy</p>
                                    </div>
                                </div>
                                <div class="cert-item">
                                    <span class="cert-icon">🧠</span>
                                    <div class="cert-details">
                                        <h5>AI/ML Machine Learning</h5>
                                        <p>Acmegrade</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="about-education">
                            <h4>Education</h4>
                            <div class="education-timeline">
                                <div class="education-item">
                                    <div class="education-header">
                                        <h5>Bachelor of Engineering in Information Technology</h5>
                                        <span class="education-date">2022 - 2026</span>
                                    </div>
                                    <p class="education-institution">Government Engineering College, Bhavnagar</p>
                                    <p class="education-grade">CGPA: 7.17</p>
                                </div>
                                <div class="education-item">
                                    <div class="education-header">
                                        <h5>12th Science</h5>
                                        <span class="education-date">March 2022</span>
                                    </div>
                                    <p class="education-institution">Belur Vidhyalaya, Mahuva</p>
                                    <p class="education-grade">Percentage: 73.69%</p>
                                </div>
                                <div class="education-item">
                                    <div class="education-header">
                                        <h5>10th</h5>
                                        <span class="education-date">March 2020</span>
                                    </div>
                                    <p class="education-institution">Belur Vidhyalaya, Mahuva</p>
                                    <p class="education-grade">Percentage: 84.16%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="skills-section">
                        <h3>Technical Skills</h3>
                        <div class="skills-grid">
                            <div class="skill-category">
                                <h4>Frontend</h4>
                                <div class="skill-bars">
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>HTML/CSS</span>
                                            <span>90%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="90"></div>
                                        </div>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>JavaScript</span>
                                            <span>80%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="85"></div>
                                        </div>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>Angular</span>
                                            <span>75%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="85"></div>
                                        </div>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>React</span>
                                            <span>70%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="80"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="skill-category">
                                <h4>Backend</h4>
                                <div class="skill-bars">
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>SQL/MongoDB</span>
                                            <span>80%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="80"></div>
                                        </div>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>Node</span>
                                            <span>75%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="85"></div>
                                        </div>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>C/C++</span>
                                            <span>85%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="85"></div>
                                        </div>
                                    </div>
                                    <div class="skill-bar">
                                        <div class="skill-info">
                                            <span>Python</span>
                                            <span>88%</span>
                                        </div>
                                        <div class="skill-progress">
                                            <div class="skill-fill" data-width="88"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="hero-stats glass-card">
                            <div class="stat-item">
                                <span class="stat-number gradient-text" data-target="5">5+</span>
                                <span class="stat-label">Projects</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number gradient-text" data-target="6">6+</span>
                                <span class="stat-label">Competitions</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number gradient-text" data-target="7.17">7.17</span>
                                <span class="stat-label">CGPA</span>
                            </div>
                        </div>
                        <div class="tools-section">
                            <h4>Tools & Technologies</h4>
                            <div class="tools-grid">
                                <div class="tool-item">
                                    <span class="tool-icon"><svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                            width="50" height="50" viewBox="0 0 48 48">
                                            <linearGradient id="goqfu1ZNmEnUrQDJEQ1bUa_l75OEUJkPAk4_gr1" x1="10.458"
                                                x2="26.314" y1="12.972" y2="26.277" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#26abe7"></stop>
                                                <stop offset="1" stop-color="#086dbf"></stop>
                                            </linearGradient>
                                            <path fill="url(#goqfu1ZNmEnUrQDJEQ1bUa_l75OEUJkPAk4_gr1)"
                                                d="M24.047,5c-1.555,0.005-2.633,0.142-3.936,0.367c-3.848,0.67-4.549,2.077-4.549,4.67V14h9v2 H15.22h-4.35c-2.636,0-4.943,1.242-5.674,4.219c-0.826,3.417-0.863,5.557,0,9.125C5.851,32.005,7.294,34,9.931,34h3.632v-5.104 c0-2.966,2.686-5.896,5.764-5.896h7.236c2.523,0,5-1.862,5-4.377v-8.586c0-2.439-1.759-4.263-4.218-4.672 C27.406,5.359,25.589,4.994,24.047,5z M19.063,9c0.821,0,1.5,0.677,1.5,1.502c0,0.833-0.679,1.498-1.5,1.498 c-0.837,0-1.5-0.664-1.5-1.498C17.563,9.68,18.226,9,19.063,9z">
                                            </path>
                                            <linearGradient id="goqfu1ZNmEnUrQDJEQ1bUb_l75OEUJkPAk4_gr2" x1="35.334"
                                                x2="23.517" y1="37.911" y2="21.034" gradientUnits="userSpaceOnUse">
                                                <stop offset="0" stop-color="#feb705"></stop>
                                                <stop offset="1" stop-color="#ffda1c"></stop>
                                            </linearGradient>
                                            <path fill="url(#goqfu1ZNmEnUrQDJEQ1bUb_l75OEUJkPAk4_gr2)"
                                                d="M23.078,43c1.555-0.005,2.633-0.142,3.936-0.367c3.848-0.67,4.549-2.077,4.549-4.67V34h-9v-2 h9.343h4.35c2.636,0,4.943-1.242,5.674-4.219c0.826-3.417,0.863-5.557,0-9.125C41.274,15.995,39.831,14,37.194,14h-3.632v5.104 c0,2.966-2.686,5.896-5.764,5.896h-7.236c-2.523,0-5,1.862-5,4.377v8.586c0,2.439,1.759,4.263,4.218,4.672 C19.719,42.641,21.536,43.006,23.078,43z M28.063,39c-0.821,0-1.5-0.677-1.5-1.502c0-0.833,0.679-1.498,1.5-1.498 c0.837,0,1.5,0.664,1.5,1.498C29.563,38.32,28.899,39,28.063,39z">
                                            </path>
                                        </svg></span>
                                    <span>Python</span>
                                </div>
                                <div class="tool-item">
                                    <span class="tool-icon"><svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                            width="50" height="50" viewBox="0 0 40 40">
                                            <path fill="#4e7ab5"
                                                d="M20,28.9C8.598,28.9,0,25.17,0,20s8.598-9,20-9s20,3.83,20,9S31.402,28.9,20,28.9z M20,13.932 c-9.729,0-17.125,3.266-17.125,6.131S10.271,26.068,20,26.068s17.125-3.266,17.125-6.131S29.729,13.932,20,13.932z">
                                            </path>
                                            <path fill="#4e7ab5"
                                                d="M12.402,38C12.401,38,12.402,38,12.402,38c-0.931,0-1.781-0.216-2.528-0.642 c-1.22-0.697-2.095-1.928-2.532-3.562c-1.146-4.282,0.703-11.482,4.713-18.344C16.76,7.407,23.007,2.003,27.599,2.003 c0.93,0,1.78,0.216,2.527,0.642c1.218,0.695,2.094,1.925,2.531,3.558c1.147,4.282-0.703,11.483-4.715,18.345 C23.241,32.594,16.995,38,12.402,38z M27.599,5.003c-2.888,0-8.409,4.193-12.954,11.963c-4.123,7.056-5.332,12.909-4.404,16.054 c0.251,0.849,0.605,1.438,1.121,1.732c2.361,1.348,8.809-2.85,13.991-11.717c4.125-7.057,5.46-12.785,4.406-16.055 c-0.271-0.841-0.604-1.435-1.119-1.728C28.347,5.084,28.006,5.003,27.599,5.003z">
                                            </path>
                                            <path fill="#4e7ab5"
                                                d="M27.599,37.997C27.599,37.997,27.599,37.997,27.599,37.997c-4.597-0.001-10.843-5.405-15.544-13.449 c-4.01-6.862-5.859-14.063-4.713-18.344C7.779,4.57,8.654,3.339,9.873,2.643C10.621,2.216,11.471,2,12.4,2 c4.595,0,10.84,5.406,15.542,13.452c4.011,6.861,5.86,14.062,4.714,18.345c-0.438,1.633-1.313,2.863-2.53,3.558 C29.379,37.781,28.528,37.997,27.599,37.997z M12.4,5c-0.407,0-0.747,0.082-1.04,0.248c-0.515,0.294-0.874,0.881-1.12,1.732 c-0.928,3.208,0.281,8.999,4.404,16.055c4.541,7.769,10.063,11.962,12.954,11.962l0,0c0.408,0,0.748-0.082,1.041-0.249 c0.514-0.292,0.883-0.876,1.118-1.728c0.867-3.146-0.281-9-4.405-16.055C20.811,9.194,15.29,5,12.4,5z">
                                            </path>
                                            <path fill="#8bb7f0"
                                                d="M23.5,20c0,1.935-1.565,3.5-3.5,3.5s-3.5-1.565-3.5-3.5s1.565-3.5,3.5-3.5S23.5,18.065,23.5,20z">
                                            </path>
                                            <path fill="#4e7ab5"
                                                d="M20,24c-2.206,0-4-1.794-4-4s1.794-4,4-4s4,1.794,4,4S22.206,24,20,24z M20,17c-1.654,0-3,1.346-3,3 s1.346,3,3,3s3-1.346,3-3S21.654,17,20,17z">
                                            </path>
                                            <path fill="#8bb7f0"
                                                d="M20,28.068C9.346,28.068,1,24.524,1,20s8.346-8.068,19-8.068S39,15.476,39,20 S30.654,28.068,20,28.068z M20,12.932c-9.757,0-18,3.237-18,7.068s8.243,7.068,18,7.068S38,23.832,38,20S29.757,12.932,20,12.932z">
                                            </path>
                                            <path fill="#8bb7f0"
                                                d="M12.402,37C12.401,37,12.402,37,12.402,37c-0.755,0-1.438-0.172-2.033-0.511 c-0.996-0.569-1.689-1.562-2.062-2.952c-1.081-4.037,0.729-10.938,4.61-17.581C17.379,8.33,23.416,3.003,27.599,3.003 c0.754,0,1.438,0.172,2.032,0.511c0.995,0.568,1.688,1.56,2.061,2.948c1.081,4.037-0.729,10.938-4.612,17.582 C22.621,31.672,16.586,37,12.402,37z M27.599,4.003c-3.784,0-9.595,5.239-13.817,12.458c-3.695,6.325-5.507,13.083-4.508,16.818 c0.301,1.123,0.836,1.91,1.592,2.342C11.307,35.872,11.823,36,12.401,36c3.785,0,9.595-5.24,13.814-12.461 c3.697-6.326,5.51-13.085,4.509-16.818c-0.3-1.121-0.835-1.908-1.59-2.338C28.693,4.131,28.177,4.003,27.599,4.003z">
                                            </path>
                                            <g>
                                                <path fill="#8bb7f0"
                                                    d="M27.599,36.997C27.599,36.997,27.599,36.997,27.599,36.997c-4.187-0.001-10.224-5.327-14.681-12.953 C9.036,17.401,7.227,10.5,8.308,6.463c0.372-1.39,1.065-2.383,2.062-2.952C10.964,3.172,11.647,3,12.4,3 c4.185,0,10.221,5.328,14.679,12.956c3.883,6.642,5.692,13.543,4.61,17.582c-0.371,1.389-1.064,2.381-2.059,2.948 C29.036,36.825,28.353,36.997,27.599,36.997z M12.4,4c-0.577,0-1.094,0.128-1.535,0.379c-0.756,0.432-1.291,1.219-1.592,2.342 c-0.999,3.734,0.813,10.493,4.508,16.818C18,30.757,23.812,35.996,27.599,35.997l0,0c0.578,0,1.095-0.128,1.536-0.38 c0.754-0.43,1.289-1.217,1.589-2.338c1-3.735-0.812-10.494-4.508-16.818C21.996,9.241,16.187,4,12.4,4z">
                                                </path>
                                            </g>
                                        </svg></span>
                                    <span>React</span>
                                </div>
                                <div class="tool-item">
                                    <span class="tool-icon"><svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                            width="50" height="50" viewBox="0 0 48 48">
                                            <path fill="#4191fd"
                                                d="M35,43c-5.514,0-10-4.486-10-10c0-3.562,1.916-6.88,5-8.66l3,5.196c-1.233,0.712-2,2.039-2,3.464 c0,2.168,1.832,4,4,4s4-1.832,4-4c0-1.425-0.767-2.752-2-3.464l3-5.196c3.084,1.78,5,5.099,5,8.66C45,38.514,40.514,43,35,43z">
                                            </path>
                                            <path fill="#f52537"
                                                d="M29,22.629l-3-5.196c1.233-0.712,2-2.04,2-3.464c0-2.168-1.832-4-4-4s-4,1.832-4,4 c0,1.425,0.767,2.752,2,3.464l-3,5.196c-3.084-1.781-5-5.1-5-8.661c0-5.514,4.486-10,10-10s10,4.486,10,10 C34,17.53,32.084,20.849,29,22.629z">
                                            </path>
                                            <path fill="#6dd669"
                                                d="M13.001,43.028c-3.459,0-6.827-1.793-8.678-4.997c-2.757-4.776-1.115-10.903,3.66-13.661 c3.085-1.781,6.917-1.781,10,0l-3,5.196c-1.234-0.713-2.766-0.713-4,0c-1.878,1.084-2.548,3.586-1.465,5.464 c1.084,1.877,3.586,2.549,5.465,1.464c1.233-0.712,2-2.04,2-3.464h6c0,3.561-1.916,6.879-5,8.66 C16.413,42.598,14.695,43.028,13.001,43.028z">
                                            </path>
                                        </svg></span>
                                    <span>OpenCV</span>
                                </div>
                                <div class="tool-item">
                                    <span class="tool-icon"><svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                            width="50" height="50" viewBox="0 0 48 48">
                                            <polygon fill="#ffa000" points="16,39.609 23,43.609 23,4 16,8"></polygon>
                                            <polygon fill="#ffa000" points="23,12.433 6,22.25 6,13.75 23,3.933">
                                            </polygon>
                                            <polygon fill="#ffb300" points="32,39.609 25,43.609 25,4 32,8"></polygon>
                                            <polygon fill="#ffb300" points="25,12.433 42,22.25 42,13.75 25,3.933">
                                            </polygon>
                                            <polygon fill="#ffb300" points="29,19.732 29,27.365 36,31.407 36,23.775">
                                            </polygon>
                                        </svg></span>
                                    <span>TensorFlow</span>
                                </div>
                                <div class="tool-item">
                                    <span class="tool-icon"><svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                            width="50" height="50" viewBox="0 0 48 48">
                                            <path fill="#bdbdbd"
                                                d="M23.933 2L3 9.285 6.308 36.408 23.955 46 41.693 36.278 45 9.156z">
                                            </path>
                                            <path fill="#b71c1c" d="M42.818 10.527L24 4.135 24 43.695 39.832 35.017z">
                                            </path>
                                            <path fill="#dd2c00"
                                                d="M23.941 4.115L5.181 10.644 8.168 35.143 23.951 43.721 24 43.695 24 4.135z">
                                            </path>
                                            <path fill="#bdbdbd" d="M24 5.996L24 15.504 32.578 34 36.987 34z"></path>
                                            <path fill="#eee" d="M11.013 34L15.422 34 24 15.504 24 5.996z"></path>
                                            <path fill="#bdbdbd" d="M24 24H30V28H24z"></path>
                                            <path fill="#eee" d="M18 24H24V28H18z"></path>
                                        </svg></span>
                                    <span>Angular</span>
                                </div>
                                <div class="tool-item">
                                    <span class="tool-icon"><svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"
                                            width="50" height="50" viewBox="0 0 48 48">
                                            <path fill="#5d4037"
                                                d="M42,17.3C42,37.8,24,44,24,44S6,37.8,6,17.3c0-2.5,0.2-4.6,0.4-6.3c0.3-2.5,2-4.5,4.4-5.1 C13.9,5,18.8,4,24,4s10.1,1,13.3,1.9c2.4,0.6,4.1,2.7,4.4,5.1C41.8,12.7,42,14.9,42,17.3z">
                                            </path>
                                            <path fill="#4caf50"
                                                d="M24,7c4.9,0,9.5,1,12.5,1.8c1.2,0.3,2,1.3,2.2,2.6c0.2,1.9,0.3,3.9,0.3,5.9c0,15.6-11.5,21.9-15,23.4 c-3.5-1.6-15-7.9-15-23.4c0-2,0.1-4,0.3-5.9c0.1-1.3,1-2.3,2.2-2.6C14.5,8,19.1,7,24,7 M24,4c-5.2,0-10.1,1-13.3,1.9 C8.4,6.5,6.6,8.6,6.4,11C6.2,12.7,6,14.9,6,17.3C6,37.8,24,44,24,44s18-6.2,18-26.7c0-2.5-0.2-4.6-0.4-6.3c-0.3-2.5-2-4.5-4.4-5.1 C34.1,5,29.2,4,24,4L24,4z">
                                            </path>
                                            <path fill="#dcedc8" d="M23 28H25V36H23z"></path>
                                            <path fill="#4caf50"
                                                d="M24,10c0,0-6,5-6,13c0,5.2,3.3,8.5,5,10l1-3l1,3c1.7-1.5,5-4.8,5-10C30,15,24,10,24,10z">
                                            </path>
                                            <path fill="#81c784" d="M24,10c0,0-6,5-6,13c0,5.2,3.3,8.5,5,10l1-3V10z">
                                            </path>
                                        </svg></span>
                                    <span>MongoDB</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="projects" class="projects section-padding">
        <div class="container">
            <h2 class="section-title">Featured Projects</h2>

            <!-- Featured Project Showcase -->
            <div class="featured-project glass-card" id="featured-project">
                <div class="featured-content">
                    <div class="featured-info">
                        <span class="featured-badge">Featured Project</span>
                        <h3 class="featured-title">Lunar DEM Generation (LDEM)</h3>
                        <p class="featured-description">
                            Developed a comprehensive Digital Elevation Model (DEM) pipeline using mono images for lunar surface mapping.
                            This project was part of the ISRO Bharatiya Antariksh Hackathon, utilizing advanced computer vision techniques.
                        </p>
                        <div class="featured-tech">
                            <span class="tech-tag">Python</span>
                            <span class="tech-tag">OpenCV</span>
                            <span class="tech-tag">QGIS</span>
                            <span class="tech-tag">Computer Vision</span>
                        </div>
                        <div class="featured-actions">
                            <!-- <a href="#" class="btn-primary">View Project</a> -->
                            <!-- <a href="#" class="btn-secondary">View Code</a> -->
                        </div>
                    </div>
                    <div class="featured-visual">
                        <div class="featured-image">
                            <div class="image-placeholder">🌙</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Projects Grid -->
            <div class="projects-grid" id="projects-grid">
                <!-- Projects will be dynamically loaded -->
            </div>
        </div>
    </section>

    <!-- Project Modal -->
    <div class="project-modal" id="project-modal">
        <div class="modal-overlay" id="modal-overlay"></div>
        <div class="modal-content">
            <button class="modal-close" id="modal-close">&times;</button>
            <div class="modal-header">
                <h2 id="modal-title"></h2>
                <div class="modal-tags" id="modal-tags"></div>
            </div>
            <div class="modal-body">
                <div class="modal-image-gallery">
                    <div class="main-image">
                        <img id="modal-main-image" src="" alt="" />
                    </div>
                    <div class="image-thumbnails" id="modal-thumbnails"></div>
                </div>
                <div class="modal-details">
                    <div class="project-description">
                        <h3>Project Overview</h3>
                        <p id="modal-description"></p>
                    </div>
                    <div class="project-features">
                        <h3>Key Features</h3>
                        <ul id="modal-features"></ul>
                    </div>
                    <div class="project-tech">
                        <h3>Technologies Used</h3>
                        <div class="tech-stack" id="modal-tech"></div>
                    </div>
                    <div class="project-actions">
                        <a href="#" id="modal-live-link" class="btn-primary" target="_blank">
                            <span>View Live</span>
                            <i class="arrow">→</i>
                        </a>
                        <a href="#" id="modal-github-link" class="btn-secondary" target="_blank">
                            <span>View Code</span>
                            <i class="arrow">→</i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <section id="contact" class="contact">
        <div class="container">
            <h2 class="section-title">Let's Work Together</h2>
            <div class="contact-content">
                <div class="contact-grid">
                    <div class="contact-info">
                        <div class="contact-intro">
                            <h3>Get In Touch</h3>
                            <p>
                                Ready to collaborate on innovative projects? I'd love to hear
                                about your ideas and discuss how we can work together to
                                create impactful solutions using cutting-edge technologies.
                            </p>
                        </div>

                        <div class="contact-methods">
                            <div class="contact-method glass-card glow-on-hover">
                                <div class="method-icon">📧</div>
                                <div class="method-details">
                                    <h4>Email</h4>
                                    <p><EMAIL></p>
                                </div>
                            </div>
                            <div class="contact-method glass-card glow-on-hover">
                                <div class="method-icon">📱</div>
                                <div class="method-details">
                                    <h4>Phone</h4>
                                    <p>+91 8980657423</p>
                                </div>
                            </div>
                            <div class="contact-method glass-card glow-on-hover">
                                <div class="method-icon">📍</div>
                                <div class="method-details">
                                    <h4>Location</h4>
                                    <p>Bhavnagar, Gujarat, India</p>
                                </div>
                            </div>
                        </div>

                        <div class="social-links">
                            <h4>Follow Me</h4>
                            <div class="social-icons">
                                <a href="#" class="social-link magnetic" aria-label="LinkedIn">
                                    <span>💼</span>
                                </a>
                                <a href="#" class="social-link magnetic" aria-label="GitHub">
                                    <span>🐙</span>
                                </a>
                                <a href="#" class="social-link magnetic" aria-label="Twitter">
                                    <span>🐦</span>
                                </a>
                                <a href="#" class="social-link magnetic" aria-label="Dribbble">
                                    <span>🏀</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="contact-form-container">
                        <form class="contact-form" id="contact-form">
                            <div class="form-group">
                                <label for="name">Full Name</label>
                                <input type="text" id="name" name="name" required />
                                <span class="form-error" id="name-error"></span>
                            </div>

                            <div class="form-group">
                                <label for="email">Email Address</label>
                                <input type="email" id="email" name="email" required />
                                <span class="form-error" id="email-error"></span>
                            </div>

                            <div class="form-group">
                                <label for="subject">Subject</label>
                                <input type="text" id="subject" name="subject" required />
                                <span class="form-error" id="subject-error"></span>
                            </div>

                            <div class="form-group">
                                <label for="message">Message</label>
                                <textarea id="message" name="message" rows="5" required></textarea>
                                <span class="form-error" id="message-error"></span>
                            </div>

                            <button type="submit" class="btn-primary submit-btn">
                                <span class="btn-text">Send Message</span>
                                <span class="btn-loading">Sending...</span>
                                <i class="arrow">→</i>
                            </button>
                        </form>

                        <div class="form-success" id="form-success">
                            <div class="success-icon">✅</div>
                            <h3>Message Sent!</h3>
                            <p>
                                Thank you for reaching out. I'll get back to you as soon as
                                possible.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script src="script.js"></script>
</body>

</html>